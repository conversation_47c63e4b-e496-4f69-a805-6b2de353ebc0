import React, { useState, useRef, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "../../hooks/use-toast";
import { useAdminProducts, useCreateProduct, useUpdateProduct, useDeleteProduct } from "../../hooks/use-admin-api";
import {
  Loader2,
  Save,
  Plus,
  Trash2,
  Edit,
  X,
  Check,
  Image,
  Eye,
  Upload,
  Globe,
  Search,
  Filter,
  RotateCcw,
  FileText,
  Download,
  MoreHorizontal,
  CheckCircle2,
  Package,
} from "lucide-react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { Badge } from "../ui/badge";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { DocumentSelector } from "../admin/DocumentSelector";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetClose,
} from "../ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";

// Product categories
const CATEGORIES = [
  { id: "aluminum", name: "Aluminum" },
  { id: "polyethylene", name: "Polyethylene" },
  { id: "steel", name: "Steel" },
  { id: "cast_iron", name: "Cast Iron" },
  { id: "facade_systems", name: "Facade Systems" },
  { id: "components", name: "Components" },
  { id: "lighting", name: "Lighting" },
];

// Product status options
const PRODUCT_STATUS = [
  { value: "in stock", label: "In Stock" },
  { value: "on request", label: "On Request" },
  { value: "limited", label: "Limited Stock" },
  { value: "discontinued", label: "Discontinued" },
];

// Validation schema for product
const productSchema = z.object({
  productId: z
    .string()
    .min(3, "Product ID must be at least 3 characters")
    .regex(
      /^[a-z0-9-]+$/,
      "Product ID must contain only lowercase letters, numbers, and hyphens",
    ),

  // Basic fields (English only)
  title: z.string().min(2, "Title is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),

  // Common fields
  category: z.string().min(1, "Category is required"),
  status: z.string().min(1, "Status is required"),
  image: z.any().optional(),
  imageUrl: z.string().optional(),

  // Structured data
  features: z.array(z.string()).optional(),
  applications: z.array(z.string()).optional(),
  specifications: z.record(z.string()).optional(),

  // Associated documents
  documentIds: z.array(z.number()).optional(),
});

type ProductForm = z.infer<typeof productSchema>;

// Interface for product data from API
interface Product {
  id: number;
  productId: string;
  title: string;
  description: string;
  category: string;
  status: string;
  image?: string;
  features?: string[];
  applications?: string[];
  specifications?: Record<string, string>;
}

// Interface for product filters
interface ProductFilters {
  searchTerm: string;
  category: string;
  status: string;
}

// Interface for document type
interface Document {
  id: number;
  title: string;
  description: string | null;
  fileUrl: string;
  fileType: string;
  documentType: string;
}

function ProductCard({
  product,
  onEdit,
  onDelete,
  onView,
}: {
  product: Product;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onView: (id: string) => void;
}) {
  return (
    <Card className="overflow-hidden border border-neutral-200 h-full flex flex-col">
      {/* Product Image */}
      <div className="aspect-video bg-gray-50 border-b border-neutral-100">
        <img
          src={
            product.image?.startsWith("/") ? product.image : `/${product.image}`
          }
          alt={product.title}
          className="w-full h-full object-contain p-2"
          onError={(e) => {
            if (import.meta.env.DEV) console.log("Admin product image failed to load:", product.image);
            // Set a transparent placeholder if image fails to load
            e.currentTarget.style.opacity = "0.3";
          }}
        />
      </div>

      <CardHeader className="p-4 pb-2 space-y-1">
        <div className="flex justify-between items-start mb-1">
          <CardTitle className="text-base font-medium line-clamp-1">
            {product.title}
          </CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-7 w-7">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40">
              <DropdownMenuItem
                onClick={() => onView(product.productId)}
                className="cursor-pointer"
              >
                <Eye className="h-4 w-4 mr-2" />
                View
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onEdit(product.productId)}
                className="cursor-pointer"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete(product.productId)}
                className="cursor-pointer text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <CardDescription className="line-clamp-2 text-xs">
          {product.description.substring(0, 120)}
          {product.description.length > 120 ? "..." : ""}
        </CardDescription>
      </CardHeader>
      <CardContent className="p-4 pt-2 flex-grow">
        <div className="flex flex-wrap gap-1 mb-2">
          <Badge variant="outline" className="text-xs bg-blue-50">
            {CATEGORIES.find((c) => c.id === product.category)?.name ||
              product.category}
          </Badge>
          <Badge variant="outline" className="text-xs bg-green-50">
            {product.status}
          </Badge>
        </div>
      </CardContent>
      <CardFooter className="p-3 pt-0 flex justify-between border-t border-neutral-100 mt-auto">
        <Button
          size="sm"
          variant="ghost"
          className="h-8 text-xs"
          onClick={() => onView(product.productId)}
        >
          <Eye className="h-3.5 w-3.5 mr-1" />
          View
        </Button>
        <Button
          size="sm"
          variant="ghost"
          className="h-8 text-xs"
          onClick={() => onEdit(product.productId)}
        >
          <Edit className="h-3.5 w-3.5 mr-1" />
          Edit
        </Button>
      </CardFooter>
    </Card>
  );
}

export function ProductsEditor() {
  const [activeTab, setActiveTab] = useState("all");
  const [isAddProductOpen, setIsAddProductOpen] = useState(false);
  const [viewProduct, setViewProduct] = useState<string | null>(null);
  const [editingProductId, setEditingProductId] = useState<string | null>(null);
  const [selectedDocuments, setSelectedDocuments] = useState<number[]>([]);

  // State for managing form data
  const [features, setFeatures] = useState<string[]>([]);
  const [applications, setApplications] = useState<string[]>([]);
  const [specifications, setSpecifications] = useState<Record<string, string>>({});

  const { toast } = useToast();

  // Debug logging
  if (import.meta.env.DEV) {
    console.log('[ProductsEditor] Component mounted/rendered');
  }

  // Fetch all available documents
  const { data: documents, isLoading: isLoadingDocuments } = useQuery({
    queryKey: ["/api/documents"],
    queryFn: async () => {
      const res = await fetch(`/api/documents?language=en`);
      if (!res.ok) throw new Error("Failed to fetch documents");
      const data = await res.json();
      return data || [];
    },
    enabled: isAddProductOpen, // Only fetch when the dialog is open
  });

  // Fetch documents for a specific product when editing
  const { data: productDocuments, isLoading: isLoadingProductDocs } = useQuery({
    queryKey: ["/api/products", editingProductId, "documents"],
    queryFn: async () => {
      if (!editingProductId) return [];

      const API_BASE_URL = import.meta.env.VITE_API_URL || (
        import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
      );

      const res = await fetch(`${API_BASE_URL}/api/products/${editingProductId}/documents`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!res.ok) {
        console.error('Failed to fetch product documents for editing:', {
          status: res.status,
          statusText: res.statusText,
          editingProductId
        });
        throw new Error(`Failed to fetch product documents: ${res.status} ${res.statusText}`);
      }

      return res.json();
    },
    enabled: !!editingProductId && isAddProductOpen,
  });

  // State for feature list and application list
  const [featureInput, setFeatureInput] = useState("");
  const [applicationInput, setApplicationInput] = useState("");

  // State for specifications
  const [specKey, setSpecKey] = useState("");
  const [specValue, setSpecValue] = useState("");

  // QueryClient for cache management
  const queryClient = useQueryClient();

  // Admin mutations
  const createProductMutation = useCreateProduct();
  const updateProductMutation = useUpdateProduct();
  const deleteProductMutation = useDeleteProduct();

  // Legacy mutation for creating/updating products (keeping for compatibility)
  const legacyUpdateProductMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const action = editingProductId ? "update" : "create";
      const productId = editingProductId || formData.get("productId");

      // Get API base URL for production compatibility
      const API_BASE_URL = import.meta.env.VITE_API_URL || (
        import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
      );

      // Fix URL paths to ensure we're using the admin API endpoints
      const url = editingProductId
        ? `${API_BASE_URL}/api/admin/products/${editingProductId}`
        : `${API_BASE_URL}/api/admin/products`;

      if (import.meta.env.DEV) console.log(`Sending ${action} request to ${url}`);

      // Log form data keys for debugging
      if (import.meta.env.DEV) console.log("FormData keys:");
      const formDataKeys = [];
      formData.forEach((value, key) => {
        formDataKeys.push(key);
        if (import.meta.env.DEV) console.log(
          `- ${key}: ${typeof value === "object" ? "File or Object" : value}`,
        );
      });

      const res = await fetch(url, {
        method: editingProductId ? "PUT" : "POST",
        body: formData,
        credentials: 'include',
        headers: {
          // Don't set Content-Type for FormData, let the browser set it with boundary
        }
      });

      // First, get the raw response to analyze
      const responseText = await res.text();
      if (import.meta.env.DEV) console.log(`Server response (status ${res.status}):`, responseText);

      if (!res.ok) {
        try {
          const errorData = JSON.parse(responseText);
          throw new Error(errorData.message || `Failed to ${action} product`);
        } catch (parseError) {
          console.error("Failed to parse error response:", parseError);
          if (
            responseText.includes("<!DOCTYPE html>") ||
            responseText.includes("<html>")
          ) {
            console.error(
              "Server returned HTML instead of JSON. Likely a server error page.",
            );
          }
          throw new Error(
            `Unexpected server response. Status: ${res.status}. Check console for details.`,
          );
        }
      }

      // Try to parse the success response
      try {
        const json = JSON.parse(responseText);
        return json;
      } catch (parseError) {
        console.error("Failed to parse success response:", parseError);
        if (responseText.trim() === "") {
          if (import.meta.env.DEV) console.log("Empty response from server - treating as success");
          return { success: true };
        } else if (
          responseText.includes("<!DOCTYPE html>") ||
          responseText.includes("<html>")
        ) {
          console.error(
            "Server returned HTML instead of JSON. This is likely a server error.",
          );
        }
        throw new Error(
          "Server returned an invalid response format. Check console for details.",
        );
      }
    },
    onSuccess: () => {
      // Invalidate all cached queries to ensure fresh data everywhere
      queryClient.invalidateQueries();

      // Specifically target and refetch the products
      queryClient.refetchQueries({ queryKey: ["/api/products"] });

      // Also refetch specific product query if editing
      if (editingProductId) {
        queryClient.refetchQueries({
          queryKey: ["/api/products", editingProductId],
        });
      }

      // Log cache invalidation
      if (import.meta.env.DEV) console.log("Complete cache invalidation performed");

      toast({
        title: editingProductId ? "Product updated" : "Product created",
        description: editingProductId
          ? "The product has been successfully updated."
          : "New product has been successfully created.",
        variant: "default",
      });
      setIsAddProductOpen(false);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message || "Something went wrong. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Product form - Initialize form before any useEffects that use it
  const form = useForm<ProductForm>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      productId: "",
      title: "",
      description: "",
      category: CATEGORIES[0].id || "aluminum", // Fallback to aluminum if first category isn't available
      status: "in stock", // Using the exact string from PRODUCT_STATUS
      features: [],
      applications: [],
      specifications: {},
      documentIds: [],
    },
  });

  // Update selected documents when product documents change
  useEffect(() => {
    if (productDocuments && productDocuments.length > 0) {
      const docIds = productDocuments.map((doc: Document) => doc.id);
      setSelectedDocuments(docIds);
      form.setValue("documentIds", docIds);
    } else if (productDocuments) {
      setSelectedDocuments([]);
      form.setValue("documentIds", []);
    }
  }, [productDocuments, form]);

  // Fetch products using admin hook
  const { data: products, isLoading: isLoadingProducts, error: productsError } = useAdminProducts();

  // Debug logging for products
  useEffect(() => {
    if (import.meta.env.DEV) {
      console.log('[ProductsEditor] Products data:', products);
      console.log('[ProductsEditor] Loading state:', isLoadingProducts);
      console.log('[ProductsEditor] Error state:', productsError);
    }
  }, [products, isLoadingProducts, productsError]);

  // Show error toast if products fail to load
  useEffect(() => {
    if (productsError) {
      console.error('[ProductsEditor] Products error:', productsError);
      toast({
        title: "Error loading products",
        description: "Failed to load products data. Please try refreshing the page.",
        variant: "destructive"
      });
    }
  }, [productsError, toast]);

  // Reset form when closing dialog or opening for new product (but not when opening for edit)
  useEffect(() => {
    if (!isAddProductOpen) {
      // Only reset when dialog is closed
      form.reset({
        productId: "",
        title: "",
        description: "",
        category: CATEGORIES[0].id,
        status: "in stock",
        features: [],
        applications: [],
        specifications: {},
        documentIds: [],
      });
      setEditingProductId(null);
      setFeatureInput("");
      setApplicationInput("");
      setSelectedDocuments([]);
      setFeatures([]);
      setApplications([]);
      setSpecifications({});
    } else if (isAddProductOpen && !editingProductId) {
      // Only reset when opening for new product (not edit)
      // Add a small delay to ensure the dialog is fully open before resetting
      const timer = setTimeout(() => {
        form.reset({
          productId: "",
          title: "",
          description: "",
          category: CATEGORIES[0].id,
          status: "in stock",
          features: [],
          applications: [],
          specifications: {},
          documentIds: [],
        });
        setFeatureInput("");
        setApplicationInput("");
        setSelectedDocuments([]);
        setFeatures([]);
        setApplications([]);
        setSpecifications({});
      }, 50);

      return () => clearTimeout(timer);
    }
  }, [isAddProductOpen, editingProductId, form]);

  // When editing product, fetch data and populate form
  useEffect(() => {
    if (editingProductId && isAddProductOpen) {
      const fetchProductData = async () => {
        try {
          if (import.meta.env.DEV) {
            console.log('[ProductsEditor] Fetching product data for edit:', editingProductId);
          }

          // Get API base URL for production compatibility
          const API_BASE_URL = import.meta.env.VITE_API_URL || (
            import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
          );

          // Use admin endpoint for fetching product data during editing
          const res = await fetch(
            `${API_BASE_URL}/api/admin/products/${editingProductId}?language=en`,
            {
              credentials: 'include',
              headers: {
                'Content-Type': 'application/json'
              }
            }
          );

          if (import.meta.env.DEV) {
            console.log('[ProductsEditor] Fetch response status:', res.status);
            console.log('[ProductsEditor] Fetch response headers:', Object.fromEntries(res.headers.entries()));
          }

          if (res.ok) {
            const responseData = await res.json();

            if (import.meta.env.DEV) {
              console.log('[ProductsEditor] Raw response data:', responseData);
            }

            // Handle response format: { success: true, product: {...} } or direct product object
            let product;
            if (responseData.success && responseData.product) {
              product = responseData.product;
            } else if (responseData.productId || responseData.title) {
              // Direct product object
              product = responseData;
            } else {
              throw new Error('Invalid response format: no product data found');
            }

            if (product) {
              if (import.meta.env.DEV) {
                console.log('[ProductsEditor] Extracted product data:', product);
              }

              // Wait a bit to ensure the form is ready
              await new Promise(resolve => setTimeout(resolve, 100));

              // Populate form fields with product data using reset to ensure all fields are updated
              const formData = {
                productId: product.productId || editingProductId,
                title: product.title || "",
                description: product.description || "",
                category: product.category || CATEGORIES[0].id,
                status: product.status || "in stock",
                features: Array.isArray(product.features) ? product.features : [],
                applications: Array.isArray(product.applications) ? product.applications : [],
                specifications: product.specifications && typeof product.specifications === 'object' ? product.specifications : {},
                imageUrl: product.image || "",
                documentIds: [],
              };

              if (import.meta.env.DEV) {
                console.log('[ProductsEditor] Form data to be set:', formData);
              }

              // Reset the form with the product data
              form.reset(formData);

              // Also populate the local state for features and applications
              setFeatures(formData.features);
              setApplications(formData.applications);
              setSpecifications(formData.specifications);

              if (import.meta.env.DEV) {
                console.log('[ProductsEditor] Form populated with product data');
                console.log('[ProductsEditor] Features state set to:', formData.features);
                console.log('[ProductsEditor] Applications state set to:', formData.applications);
                console.log('[ProductsEditor] Specifications state set to:', formData.specifications);
              }

              // Force a re-render to ensure UI updates
              setTimeout(() => {
                if (import.meta.env.DEV) {
                  console.log('[ProductsEditor] Form values after population:', form.getValues());
                }
              }, 50);
            } else {
              throw new Error('No product data found in response');
            }
          } else {
            // Get response text for detailed error logging
            const errorText = await res.text();
            console.error('[ProductsEditor] Failed to fetch product:', {
              status: res.status,
              statusText: res.statusText,
              responseText: errorText,
              url: `${API_BASE_URL}/api/admin/products/${editingProductId}?language=en`
            });

            toast({
              title: "Error",
              description: `Failed to load product data: ${res.status} ${res.statusText}. Check console for details.`,
              variant: "destructive",
            });
          }
        } catch (error) {
          console.error("[ProductsEditor] Error fetching product data:", {
            error: error.message,
            stack: error.stack,
            editingProductId,
            timestamp: new Date().toISOString()
          });

          toast({
            title: "Error",
            description: `Failed to load product data: ${error.message}. Check console for details.`,
            variant: "destructive",
          });
        }
      };

      // Add a small delay before fetching to ensure the dialog is fully open
      const timer = setTimeout(fetchProductData, 150);
      return () => clearTimeout(timer);
    }
  }, [editingProductId, isAddProductOpen, form, toast]);

  // Handle form submission
  const onSubmit = async (data: ProductForm) => {
    try {
      if (editingProductId) {
        // For updates, send JSON with only the necessary fields
        const productData: any = {
          title: data.title,
          description: data.description,
          category: data.category,
          status: data.status,
        };

        // Only include features if they exist and are not empty
        if (data.features && data.features.length > 0) {
          productData.features = data.features;
        }

        // Only include applications if they exist and are not empty
        if (data.applications && data.applications.length > 0) {
          productData.applications = data.applications;
        }

        // Only include specifications if they exist and are not empty
        if (data.specifications && Object.keys(data.specifications).length > 0) {
          productData.specifications = data.specifications;
        }

        // Only include imageUrl if it exists
        if (data.imageUrl) {
          productData.imageUrl = data.imageUrl;
        }

        if (import.meta.env.DEV) {
          console.log('[ProductsEditor] Sending update data:', productData);
        }

        await updateProductMutation.mutateAsync({ id: editingProductId, productData });
      } else {
        // For creation, use FormData as before
        const formData = new FormData();
        formData.append("productId", data.productId);
        formData.append("title", data.title);
        formData.append("description", data.description);
        formData.append("category", data.category);
        formData.append("status", data.status);
        formData.append("language", "en");

        // Handle features and applications
        if (data.features && data.features.length > 0) {
          formData.append("features", JSON.stringify(data.features));
        }

        if (data.applications && data.applications.length > 0) {
          formData.append("applications", JSON.stringify(data.applications));
        }

        // Handle specifications
        if (data.specifications && Object.keys(data.specifications).length > 0) {
          formData.append("specifications", JSON.stringify(data.specifications));
        }

        // Handle associated documents
        if (data.documentIds && data.documentIds.length > 0) {
          formData.append("documentIds", JSON.stringify(data.documentIds));
        }

        // Handle image file upload if present
        if (data.image && data.image instanceof FileList && data.image.length > 0) {
          formData.append("image", data.image[0]);
        }

        await createProductMutation.mutateAsync(formData);
      }

      // Success handling
      toast({
        title: editingProductId ? "Product updated" : "Product created",
        description: editingProductId
          ? "The product has been successfully updated."
          : "New product has been successfully created.",
        variant: "default",
      });
      setIsAddProductOpen(false);
    } catch (error) {
      console.error('Failed to update product:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Something went wrong. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Add a new feature
  const handleAddFeature = () => {
    if (featureInput.trim()) {
      const newFeatures = [...features, featureInput.trim()];
      setFeatures(newFeatures);
      form.setValue("features", newFeatures);
      setFeatureInput("");
    }
  };

  // Remove a feature
  const handleRemoveFeature = (index: number) => {
    const newFeatures = features.filter((_, i) => i !== index);
    setFeatures(newFeatures);
    form.setValue("features", newFeatures);
  };

  // Add a new application
  const handleAddApplication = () => {
    if (applicationInput.trim()) {
      const newApplications = [...applications, applicationInput.trim()];
      setApplications(newApplications);
      form.setValue("applications", newApplications);
      setApplicationInput("");
    }
  };

  // Remove an application
  const handleRemoveApplication = (index: number) => {
    const newApplications = applications.filter((_, i) => i !== index);
    setApplications(newApplications);
    form.setValue("applications", newApplications);
  };

  // Add a new specification
  const handleAddSpecification = () => {
    if (specKey.trim() && specValue.trim()) {
      const newSpecs = {
        ...specifications,
        [specKey.trim()]: specValue.trim(),
      };
      setSpecifications(newSpecs);
      form.setValue("specifications", newSpecs);
      setSpecKey("");
      setSpecValue("");
    }
  };

  // Remove a specification
  const handleRemoveSpecification = (key: string) => {
    const newSpecs = { ...specifications };
    delete newSpecs[key];
    setSpecifications(newSpecs);
    form.setValue("specifications", newSpecs);
  };

  // Handle document selection change
  const handleDocumentSelectionChange = (selectedIds: number[]) => {
    setSelectedDocuments(selectedIds);
    form.setValue("documentIds", selectedIds);
  };

  // Handle edit
  const handleEdit = (productId: string) => {
    if (import.meta.env.DEV) {
      console.log('[ProductsEditor] Starting edit for product:', productId);
    }

    // Set editing state first, then open modal
    // The useEffect will handle data fetching and form population
    setEditingProductId(productId);
    setIsAddProductOpen(true);

    if (import.meta.env.DEV) {
      console.log('[ProductsEditor] Edit modal opened, data will be fetched via useEffect');
    }
  };

  // Handle delete
  const handleDelete = async (productId: string) => {
    if (
      window.confirm(
        "Are you sure you want to delete this product? This will remove the product in all languages.",
      )
    ) {
      await deleteProductMutation.mutateAsync(productId);
    }
  };

  // Handle view
  const handleView = (productId: string) => {
    setViewProduct(productId);
  };

  // Helper to get category label by ID
  const getCategoryLabel = (categoryId: string) => {
    return CATEGORIES.find((c) => c.id === categoryId)?.name || categoryId;
  };

  // Helper to format specifications for display
  const formatSpecifications = (specs: Record<string, string> = {}) => {
    return Object.entries(specs).map(([key, value]) => (
      <div
        key={key}
        className="flex justify-between border-b border-gray-100 py-1"
      >
        <span className="text-gray-600 font-medium">{key}</span>
        <span className="text-gray-800">{value}</span>
      </div>
    ));
  };

  // Search and filtering logic
  const [filters, setFilters] = useState<ProductFilters>({
    searchTerm: "",
    category: "",
    status: "",
  });

  const filteredProducts = !products
    ? []
    : products.filter((product: Product) => {
        // Filter by search term
        const searchMatch =
          !filters.searchTerm ||
          product.title
            .toLowerCase()
            .includes(filters.searchTerm.toLowerCase()) ||
          product.description
            .toLowerCase()
            .includes(filters.searchTerm.toLowerCase());

        // Filter by category
        const categoryMatch =
          !filters.category || product.category === filters.category;

        // Filter by status
        const statusMatch =
          !filters.status || product.status === filters.status;

        return searchMatch && categoryMatch && statusMatch;
      });

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilters((prev) => ({
      ...prev,
      searchTerm: event.target.value,
    }));
  };

  // Handle filter changes
  const handleFilterChange = (field: keyof ProductFilters, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      searchTerm: "",
      category: "",
      status: "",
    });
  };

  // Loading and empty states
  if (isLoadingProducts) {
    return (
      <div className="w-full h-64 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-gray-600">Loading products...</span>
      </div>
    );
  }

  // Error state
  if (productsError) {
    return (
      <div className="w-full h-64 flex flex-col items-center justify-center">
        <div className="text-red-500 mb-4">
          <X className="h-8 w-8" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load products</h3>
        <p className="text-sm text-gray-600 mb-4 text-center max-w-md">
          There was an error loading the products data. Please check your connection and try again.
        </p>
        <Button
          onClick={() => window.location.reload()}
          variant="outline"
          className="flex items-center"
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  // No products state
  if (!products || products.length === 0) {
    return (
      <div className="w-full h-64 flex flex-col items-center justify-center">
        <div className="text-gray-400 mb-4">
          <Package className="h-8 w-8" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
        <p className="text-sm text-gray-600 mb-4 text-center max-w-md">
          {filteredProducts.length === 0 && products.length > 0
            ? "No products match your current filters. Try adjusting your search criteria."
            : "Get started by adding your first product."
          }
        </p>
        <Dialog open={isAddProductOpen} onOpenChange={setIsAddProductOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center">
              <Plus className="h-4 w-4 mr-2" />
              Add First Product
            </Button>
          </DialogTrigger>
        </Dialog>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-semibold tracking-tight">Products</h2>
          <p className="text-sm text-muted-foreground">
            Manage all products in your catalog. Add, edit, or remove products.
          </p>
        </div>
        <Button
          onClick={() => {
            setIsAddProductOpen(true);
            setEditingProductId(null);
          }}
          className="sm:w-auto w-full"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Product
        </Button>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search products..."
            className="pl-8 w-full"
            value={filters.searchTerm}
            onChange={handleSearchChange}
          />
        </div>

        <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
          <Select
            value={filters.category}
            onValueChange={(value) => handleFilterChange("category", value)}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {CATEGORIES.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={filters.status}
            onValueChange={(value) => handleFilterChange("status", value)}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="All Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              {PRODUCT_STATUS.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button variant="outline" size="sm" onClick={resetFilters}>
            <RotateCcw className="h-4 w-4 mr-1" />
            Reset
          </Button>
        </div>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {filteredProducts.length === 0 ? (
          <div className="col-span-full text-center py-10">
            <FileText className="h-10 w-10 mx-auto text-gray-400 mb-2" />
            <h3 className="text-lg font-medium">No products found</h3>
            <p className="text-sm text-gray-500 mt-1">
              {filters.searchTerm || filters.category || filters.status
                ? "Try changing your filters or search term"
                : "Add your first product to get started"}
            </p>
            {(filters.searchTerm || filters.category || filters.status) && (
              <Button variant="outline" className="mt-4" onClick={resetFilters}>
                Clear Filters
              </Button>
            )}
          </div>
        ) : (
          filteredProducts.map((product: Product) => (
            <ProductCard
              key={product.productId}
              product={product}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onView={handleView}
            />
          ))
        )}
      </div>

      {/* Add/Edit Product Dialog */}
      <Dialog
        open={isAddProductOpen}
        onOpenChange={(open) => {
          if (!createProductMutation.isPending && !updateProductMutation.isPending) {
            setIsAddProductOpen(open);
          }
        }}
      >
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingProductId ? "Edit Product" : "Add New Product"}
            </DialogTitle>
            <DialogDescription>
              {editingProductId
                ? "Update the product information"
                : "Fill out the form to add a new product"}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-6 pt-4"
            >
              {/* Product ID and Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <FormField
                    control={form.control}
                    name="productId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product ID</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="unique-product-id"
                            disabled={!!editingProductId}
                          />
                        </FormControl>
                        <p className="text-xs text-muted-foreground">
                          Unique identifier, use kebab-case (e.g.,
                          aluminum-profiles)
                        </p>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Product name" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Detailed product description"
                            rows={4}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || "aluminum"} // Use value instead of defaultValue
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {CATEGORIES.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || "in stock"} // Use value instead of defaultValue
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {PRODUCT_STATUS.map((status) => (
                              <SelectItem
                                key={status.value}
                                value={status.value}
                              >
                                {status.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="image"
                    render={({ field: { value, onChange, ...fieldProps } }) => (
                      <FormItem>
                        <FormLabel>Product Image</FormLabel>
                        <FormControl>
                          <div className="space-y-2">
                            {form.getValues("imageUrl") && (
                              <div className="relative w-full h-40 bg-gray-100 rounded-md overflow-hidden">
                                <img
                                  src={form.getValues("imageUrl")}
                                  alt="Product"
                                  className="w-full h-full object-contain"
                                />
                              </div>
                            )}
                            <Input
                              {...fieldProps}
                              type="file"
                              accept="image/*"
                              onChange={(e) => {
                                onChange(e.target.files);
                              }}
                              className="flex h-10 px-3 py-2"
                            />
                          </div>
                        </FormControl>
                        <FormDescription>
                          Upload a product image (optional). Maximum size: 2MB.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Features List */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium leading-none">Features</h3>
                <p className="text-sm text-muted-foreground">
                  Add key features of the product
                </p>
                <div className="flex gap-2">
                  <Input
                    placeholder="Add a feature (e.g., High durability)"
                    value={featureInput}
                    onChange={(e) => setFeatureInput(e.target.value)}
                    className="flex-grow"
                  />
                  <Button
                    type="button"
                    onClick={handleAddFeature}
                    disabled={!featureInput.trim()}
                  >
                    Add
                  </Button>
                </div>

                <div className="space-y-1 mt-2">
                  {features.map((feature, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between rounded-md border px-3 py-2"
                    >
                      <span className="text-sm">{feature}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveFeature(index)}
                        className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Applications List */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium leading-none">
                  Applications
                </h3>
                <p className="text-sm text-muted-foreground">
                  Add common applications for this product
                </p>
                <div className="flex gap-2">
                  <Input
                    placeholder="Add an application (e.g., Construction)"
                    value={applicationInput}
                    onChange={(e) => setApplicationInput(e.target.value)}
                    className="flex-grow"
                  />
                  <Button
                    type="button"
                    onClick={handleAddApplication}
                    disabled={!applicationInput.trim()}
                  >
                    Add
                  </Button>
                </div>

                <div className="space-y-1 mt-2">
                  {applications.map((application, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between rounded-md border px-3 py-2"
                    >
                      <span className="text-sm">{application}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveApplication(index)}
                        className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Specifications */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium leading-none">
                  Specifications
                </h3>
                <p className="text-sm text-muted-foreground">
                  Add technical specifications for the product
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  <Input
                    placeholder="Specification name (e.g., Weight)"
                    value={specKey}
                    onChange={(e) => setSpecKey(e.target.value)}
                  />
                  <div className="flex gap-2">
                    <Input
                      placeholder="Value (e.g., 5kg)"
                      value={specValue}
                      onChange={(e) => setSpecValue(e.target.value)}
                      className="flex-grow"
                    />
                    <Button
                      type="button"
                      onClick={handleAddSpecification}
                      disabled={!specKey.trim() || !specValue.trim()}
                    >
                      Add
                    </Button>
                  </div>
                </div>

                <div className="space-y-1 mt-2">
                  {Object.entries(specifications).map(([key, value]) => (
                    <div
                      key={key}
                      className="flex items-center justify-between rounded-md border px-3 py-2"
                    >
                      <div className="flex-grow">
                        <span className="text-sm font-medium">{key}:</span>
                        <span className="text-sm ml-2">{value}</span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveSpecification(key)}
                        className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Documents */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium leading-none">
                  Related Documents
                </h3>
                <p className="text-sm text-muted-foreground">
                  Select documents to associate with this product
                </p>
                <DocumentSelector
                  value={selectedDocuments}
                  onChange={handleDocumentSelectionChange}
                  disabled={isLoadingDocuments}
                />
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setIsAddProductOpen(false)}
                  disabled={updateProductMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={updateProductMutation.isPending}
                >
                  {updateProductMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {editingProductId ? "Updating..." : "Creating..."}
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      {editingProductId ? "Update Product" : "Create Product"}
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Product View Sheet */}
      <Sheet
        open={!!viewProduct}
        onOpenChange={(open) => !open && setViewProduct(null)}
      >
        <SheetContent className="sm:max-w-md md:max-w-lg overflow-y-auto">
          {viewProduct && (
            <ProductView
              productId={viewProduct}
              onEdit={() => {
                handleEdit(viewProduct);
                setViewProduct(null);
              }}
            />
          )}
        </SheetContent>
      </Sheet>
    </div>
  );
}

function ProductView({
  productId,
  onEdit,
}: {
  productId: string;
  onEdit: () => void;
}) {
  // Fetch product details using admin endpoint
  const { data: product, isLoading } = useQuery({
    queryKey: ["/api/admin/products", productId],
    queryFn: async () => {
      const API_BASE_URL = import.meta.env.VITE_API_URL || (
        import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
      );

      const res = await fetch(`${API_BASE_URL}/api/admin/products/${productId}?language=en`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!res.ok) {
        const errorText = await res.text();
        console.error('Failed to fetch product for view:', {
          status: res.status,
          statusText: res.statusText,
          responseText: errorText,
          productId
        });
        throw new Error(`Failed to fetch product: ${res.status} ${res.statusText}`);
      }

      const responseData = await res.json();

      // Handle response format: { success: true, product: {...} } or direct product object
      if (responseData.success && responseData.product) {
        return responseData.product;
      } else if (responseData.productId || responseData.title) {
        return responseData;
      } else {
        throw new Error('Invalid response format: no product data found');
      }
    },
  });

  // Fetch product documents using admin endpoint
  const { data: documents } = useQuery({
    queryKey: ["/api/admin/products", productId, "documents"],
    queryFn: async () => {
      const API_BASE_URL = import.meta.env.VITE_API_URL || (
        import.meta.env.DEV ? 'http://localhost:3001' : 'https://api.metanord.eu'
      );

      const res = await fetch(`${API_BASE_URL}/api/admin/products/${productId}/documents`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!res.ok) {
        console.error('Failed to fetch product documents:', {
          status: res.status,
          statusText: res.statusText,
          productId
        });
        throw new Error(`Failed to fetch product documents: ${res.status} ${res.statusText}`);
      }

      return res.json();
    },
  });

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="h-full flex flex-col items-center justify-center">
        <FileText className="h-10 w-10 text-muted-foreground mb-2" />
        <h3 className="font-medium">Product Not Found</h3>
        <p className="text-sm text-muted-foreground mt-1">
          The requested product could not be found.
        </p>
      </div>
    );
  }

  // Get category name
  const categoryName =
    CATEGORIES.find((c) => c.id === product.category)?.name || product.category;

  return (
    <div className="space-y-6">
      <SheetHeader>
        <SheetTitle className="text-xl">{product.title}</SheetTitle>
        <SheetDescription>
          <div className="flex gap-2 mt-1">
            <Badge variant="outline" className="bg-blue-50">
              {categoryName}
            </Badge>
            <Badge variant="outline" className="bg-green-50">
              {product.status}
            </Badge>
          </div>
        </SheetDescription>
      </SheetHeader>

      {product.image && (
        <div className="w-full h-48 bg-gray-100 rounded-md overflow-hidden">
          <img
            src={product.image}
            alt={product.title}
            className="w-full h-full object-contain"
          />
        </div>
      )}

      <div className="space-y-4">
        <div>
          <h3 className="text-sm font-medium">Description</h3>
          <p className="text-sm text-gray-600 mt-1">{product.description}</p>
        </div>

        {product.features && product.features.length > 0 && (
          <div>
            <h3 className="text-sm font-medium">Features</h3>
            <ul className="mt-1 space-y-1">
              {product.features.map((feature: string, index: number) => (
                <li
                  key={index}
                  className="text-sm text-gray-600 flex items-start"
                >
                  <CheckCircle2 className="h-4 w-4 text-green-500 mr-1 mt-0.5 flex-shrink-0" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {product.applications && product.applications.length > 0 && (
          <div>
            <h3 className="text-sm font-medium">Applications</h3>
            <ul className="mt-1 space-y-1">
              {product.applications.map(
                (application: string, index: number) => (
                  <li key={index} className="text-sm text-gray-600">
                    • {application}
                  </li>
                ),
              )}
            </ul>
          </div>
        )}

        {product.specifications &&
          Object.keys(product.specifications).length > 0 && (
            <div>
              <h3 className="text-sm font-medium">Specifications</h3>
              <div className="mt-1 space-y-1">
                {Object.entries(product.specifications).map(
                  ([key, value]: [string, any]) => (
                    <div
                      key={key}
                      className="flex justify-between text-sm py-1 border-b border-gray-100"
                    >
                      <span className="text-gray-600 font-medium">{key}</span>
                      <span className="text-gray-800">
                        {typeof value === "object"
                          ? JSON.stringify(value)
                          : String(value)}
                      </span>
                    </div>
                  ),
                )}
              </div>
            </div>
          )}

        {documents && documents.length > 0 && (
          <div>
            <h3 className="text-sm font-medium">Related Documents</h3>
            <div className="mt-1 space-y-1">
              {documents.map((doc: Document) => (
                <div
                  key={doc.id}
                  className="flex items-center justify-between p-2 border rounded-md"
                >
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 text-blue-500 mr-2" />
                    <span className="text-sm">{doc.title}</span>
                  </div>
                  <a
                    href={doc.fileUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                  >
                    <Download className="h-3.5 w-3.5 mr-1" />
                    <span>Download</span>
                  </a>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="pt-4 border-t">
        <Button onClick={onEdit} variant="default" className="w-full">
          <Edit className="h-4 w-4 mr-2" />
          Edit Product
        </Button>
        <SheetClose asChild>
          <Button variant="outline" className="w-full mt-2">
            Close
          </Button>
        </SheetClose>
      </div>
    </div>
  );
}
